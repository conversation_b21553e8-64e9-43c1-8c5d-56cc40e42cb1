const { statements } = require('./dist/database/database');
const { initializeQueue, getQueue } = require('./dist/queue/queue');
const { createClient } = require('redis');

async function debugExecution(executionId) {
  console.log(`🔍 Debugging execution: ${executionId}`);

  try {
    // Get execution from database
    const execution = statements.getExecutionById.get(executionId);
    if (!execution) {
      console.log(`❌ Execution not found in database: ${executionId}`);
      return;
    }

    console.log(`📋 Execution found:`);
    console.log(`  ID: ${execution.id}`);
    console.log(`  Flow ID: ${execution.flow_id}`);
    console.log(`  Status: ${execution.status}`);
    console.log(`  Started: ${execution.started_at}`);
    console.log(`  Completed: ${execution.completed_at || 'N/A'}`);
    console.log(`  Error: ${execution.error || 'N/A'}`);

    // Get execution logs
    const logs = statements.getExecutionLogs.all(executionId);
    console.log(`\n📝 Execution logs (${logs.length} entries):`);
    logs.forEach(log => {
      console.log(`  [${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`);
    });

    // Check BullMQ job status
    console.log(`\n🔄 Checking BullMQ job status...`);

    // Initialize queue to access BullMQ
    await initializeQueue();
    const queue = getQueue();

    try {
      // Check if job exists by ID
      const job = await queue.getJob(executionId);
      if (job) {
        console.log(`  Job found: ${job.id}`);
        console.log(`  Job state: ${await job.getState()}`);
        console.log(`  Job attempts: ${job.attemptsMade}/${job.opts.attempts}`);
        console.log(`  Job progress: ${job.progress}`);
        console.log(`  Job data:`, job.data);

        if (job.failedReason) {
          console.log(`  Failed reason: ${job.failedReason}`);
        }

        if (job.finishedOn) {
          console.log(`  Finished on: ${new Date(job.finishedOn)}`);
        }

        if (job.processedOn) {
          console.log(`  Processed on: ${new Date(job.processedOn)}`);
        }
      } else {
        console.log(`  Job not found by ID: ${executionId}`);
      }

      // Check all job states for this execution
      console.log(`\n🔍 Checking all job states...`);

      // Check failed jobs
      const failedJobs = await queue.getFailed(0, 100);
      const matchingFailedJob = failedJobs.find(j => j.id === executionId || j.data?.executionId === executionId);

      if (matchingFailedJob) {
        console.log(`  Found in failed jobs:`);
        console.log(`    Job ID: ${matchingFailedJob.id}`);
        console.log(`    Attempts: ${matchingFailedJob.attemptsMade}/${matchingFailedJob.opts.attempts}`);
        console.log(`    Failed reason: ${matchingFailedJob.failedReason}`);
        console.log(`    Finished on: ${new Date(matchingFailedJob.finishedOn)}`);
        console.log(`    Data:`, matchingFailedJob.data);
      }

      // Check completed jobs
      const completedJobs = await queue.getCompleted(0, 100);
      const matchingCompletedJob = completedJobs.find(j => j.id === executionId || j.data?.executionId === executionId);

      if (matchingCompletedJob) {
        console.log(`  Found in completed jobs:`);
        console.log(`    Job ID: ${matchingCompletedJob.id}`);
        console.log(`    Return value:`, matchingCompletedJob.returnvalue);
        console.log(`    Finished on: ${new Date(matchingCompletedJob.finishedOn)}`);
      }

      // Check waiting jobs
      const waitingJobs = await queue.getWaiting(0, 100);
      const matchingWaitingJob = waitingJobs.find(j => j.id === executionId || j.data?.executionId === executionId);

      if (matchingWaitingJob) {
        console.log(`  Found in waiting jobs:`);
        console.log(`    Job ID: ${matchingWaitingJob.id}`);
        console.log(`    Data:`, matchingWaitingJob.data);
      }

      // Check active jobs
      const activeJobs = await queue.getActive(0, 100);
      const matchingActiveJob = activeJobs.find(j => j.id === executionId || j.data?.executionId === executionId);

      if (matchingActiveJob) {
        console.log(`  Found in active jobs:`);
        console.log(`    Job ID: ${matchingActiveJob.id}`);
        console.log(`    Progress: ${matchingActiveJob.progress}`);
        console.log(`    Data:`, matchingActiveJob.data);
      }

      // Check if no jobs found at all
      if (!matchingFailedJob && !matchingCompletedJob && !matchingWaitingJob && !matchingActiveJob && !job) {
        console.log(`  ⚠️ No BullMQ job found for execution ${executionId}`);
        console.log(`  This could indicate:`);
        console.log(`    - Job was removed due to retention policy`);
        console.log(`    - Job was never created`);
        console.log(`    - Different job ID was used`);
      }

    } catch (queueError) {
      console.error(`❌ Error checking queue: ${queueError.message}`);
    }

  } catch (error) {
    console.error(`❌ Error debugging execution: ${error.message}`);
  } finally {
    // Clean up queue connection
    try {
      const { closeQueue } = require('./dist/queue/queue');
      await closeQueue();
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError.message);
    }
  }
}

// Run the debug function
const executionId = process.argv[2];
if (!executionId) {
  console.log('Usage: node debug-execution.js <execution-id>');
  process.exit(1);
}

debugExecution(executionId).then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
