const { statements } = require('./dist/database/database');
const { QueueService } = require('./dist/queue/queueService');

async function debugExecution(executionId) {
  console.log(`🔍 Debugging execution: ${executionId}`);
  
  try {
    // Get execution from database
    const execution = statements.getExecutionById.get(executionId);
    if (!execution) {
      console.log(`❌ Execution not found in database: ${executionId}`);
      return;
    }
    
    console.log(`📋 Execution found:`);
    console.log(`  ID: ${execution.id}`);
    console.log(`  Flow ID: ${execution.flow_id}`);
    console.log(`  Status: ${execution.status}`);
    console.log(`  Started: ${execution.started_at}`);
    console.log(`  Completed: ${execution.completed_at || 'N/A'}`);
    console.log(`  Error: ${execution.error || 'N/A'}`);
    
    // Get execution logs
    const logs = statements.getExecutionLogs.all(executionId);
    console.log(`\n📝 Execution logs (${logs.length} entries):`);
    logs.forEach(log => {
      console.log(`  [${log.timestamp}] ${log.level.toUpperCase()}: ${log.message}`);
    });
    
    // Check BullMQ job status
    console.log(`\n🔄 Checking BullMQ job status...`);
    const queueService = new QueueService();
    
    try {
      // Check if job exists in different states
      const job = await queueService.getJobById(executionId);
      if (job) {
        console.log(`  Job found: ${job.id}`);
        console.log(`  Job state: ${await job.getState()}`);
        console.log(`  Job attempts: ${job.attemptsMade}/${job.opts.attempts}`);
        console.log(`  Job progress: ${job.progress}`);
        console.log(`  Job data:`, job.data);
        
        if (job.failedReason) {
          console.log(`  Failed reason: ${job.failedReason}`);
        }
        
        if (job.finishedOn) {
          console.log(`  Finished on: ${new Date(job.finishedOn)}`);
        }
        
        if (job.processedOn) {
          console.log(`  Processed on: ${new Date(job.processedOn)}`);
        }
      } else {
        console.log(`  Job not found in queue`);
        
        // Check failed jobs
        console.log(`\n🔍 Checking failed jobs...`);
        const failedJobs = await queueService.getJobs({ status: 'failed', limit: 100 });
        const matchingFailedJob = failedJobs.find(j => j.id === executionId || j.data.executionId === executionId);
        
        if (matchingFailedJob) {
          console.log(`  Found in failed jobs:`);
          console.log(`    Job ID: ${matchingFailedJob.id}`);
          console.log(`    Attempts: ${matchingFailedJob.attemptsMade}/${matchingFailedJob.opts.attempts}`);
          console.log(`    Failed reason: ${matchingFailedJob.failedReason}`);
          console.log(`    Finished on: ${new Date(matchingFailedJob.finishedOn)}`);
        }
        
        // Check completed jobs
        console.log(`\n🔍 Checking completed jobs...`);
        const completedJobs = await queueService.getJobs({ status: 'completed', limit: 100 });
        const matchingCompletedJob = completedJobs.find(j => j.id === executionId || j.data.executionId === executionId);
        
        if (matchingCompletedJob) {
          console.log(`  Found in completed jobs:`);
          console.log(`    Job ID: ${matchingCompletedJob.id}`);
          console.log(`    Return value:`, matchingCompletedJob.returnvalue);
          console.log(`    Finished on: ${new Date(matchingCompletedJob.finishedOn)}`);
        }
      }
    } catch (queueError) {
      console.error(`❌ Error checking queue: ${queueError.message}`);
    }
    
  } catch (error) {
    console.error(`❌ Error debugging execution: ${error.message}`);
  }
}

// Run the debug function
const executionId = process.argv[2];
if (!executionId) {
  console.log('Usage: node debug-execution.js <execution-id>');
  process.exit(1);
}

debugExecution(executionId).then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
